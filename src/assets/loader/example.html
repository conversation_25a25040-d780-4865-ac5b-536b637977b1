<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Enhanced Chat Widget with Voice Recording Demo</title>
  <style>
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }

    .demo-container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      border-radius: 20px;
      padding: 40px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    }

    .demo-header {
      text-align: center;
      margin-bottom: 40px;
    }

    .demo-header h1 {
      color: #1f2937;
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 10px;
    }

    .demo-header p {
      color: #6b7280;
      font-size: 1.1rem;
      margin: 0;
    }

    .demo-controls {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 40px;
    }

    .demo-button {
      padding: 12px 24px;
      border: none;
      border-radius: 12px;
      font-weight: 600;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s ease;
      text-align: center;
    }

    .demo-button.primary {
      background: linear-gradient(135deg, #273272 0%, #4A5BB8 100%);
      color: white;
    }

    .demo-button.primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(39, 50, 114, 0.3);
    }

    .demo-button.secondary {
      background: #f3f4f6;
      color: #374151;
      border: 1px solid #e5e7eb;
    }

    .demo-button.secondary:hover {
      background: #e5e7eb;
      transform: translateY(-1px);
    }

    .feature-showcase {
      background: #f8fafc;
      border-radius: 16px;
      padding: 30px;
      margin-bottom: 30px;
    }

    .feature-showcase h3 {
      color: #1f2937;
      font-size: 1.5rem;
      margin-bottom: 15px;
    }

    .feature-list {
      list-style: none;
      padding: 0;
    }

    .feature-list li {
      padding: 8px 0;
      color: #6b7280;
      position: relative;
      padding-left: 25px;
    }

    .feature-list li::before {
      content: '✓';
      position: absolute;
      left: 0;
      color: #10b981;
      font-weight: bold;
    }

    /* Container for zoomed image */
    #zoomedImageContainer {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background-color: rgba(0, 0, 0, 0.8);
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }

    .imageWrapper {
      position: relative;
    }

    #zoomedImage {
      max-width: 1380px;
      max-height: 900px;
      width: 100%;
      height: auto;
      display: block;
    }

    #altText {
      position: absolute;
      top: 10px;
      left: 10px;
      background-color: rgba(0, 0, 0, 0.5);
      color: #fff;
      padding: 5px 10px;
      border-radius: 10px;
      font-size: 14px;
      max-width: 80%;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }

    .closeButton {
      position: absolute;
      top: 15px;
      right: 15px;
      background-color: rgba(0, 0, 0, 0.8);
      color: white;
      border: none;
      font-size: 24px;
      padding: 8px;
      cursor: pointer;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      backdrop-filter: blur(10px);
    }

    .closeButton:hover {
      background-color: rgba(0, 0, 0, 0.9);
      transform: scale(1.1);
    }

    @media (max-width: 768px) {
      .demo-container {
        margin: 10px;
        padding: 20px;
        border-radius: 16px;
      }

      .demo-header h1 {
        font-size: 2rem;
      }

      .demo-controls {
        grid-template-columns: 1fr;
      }
    }
  </style>
  <script
    src="http://localhost:4200/assets/loader/loader.js"
    data-link="https://embed.dxconnect.lifesup.ai/"
    data-id="accfb9f5-8475-427a-a4b7-3f30f911544b"
    data-bubble-background-color="#273272"
    data-bubble-color="#ffffff"
    data-bubble-content="💬"
    data-bubble-padding="16px"
    data-bubble-border-radius="50%"
    data-bubble-size="64px"
    data-bubble-shadow="0 8px 32px rgba(39, 50, 114, 0.3)"
    data-bubble-hover-scale="1.1"
    data-widget-background-color="#ffffff"
    data-widget-color="#1f2937"
    data-widget-width="420px"
    data-widget-height="600px"
    data-widget-border-radius="20px"
    data-widget-shadow="0 20px 60px rgba(0, 0, 0, 0.15)"
    data-widget-border="1px solid rgba(0, 0, 0, 0.08)"
    data-header-background-color="#f8fafc"
    data-header-border-bottom="1px solid rgba(0, 0, 0, 0.06)"
    data-header-padding="16px 20px"
    data-button-hover-color="rgba(39, 50, 114, 0.1)"
    data-button-active-color="rgba(39, 50, 114, 0.2)"
    data-content-img="https://www.dxsuite.io/svgs/homepage/logo.svg"
    data-content-text="DxConnect AI"
    data-content-subtitle="Your AI Assistant with Voice"
    data-chat-text-size="15px"
    data-chat-line-height="1.6"
    data-assistant-background="#f3f4f6"
    data-assistant-color="#1f2937"
    data-assistant-avatar=""
    data-user-background="#273272"
    data-user-color="#ffffff"
    data-powered-by="DxConnect"
    data-powered-link="https://dxconnect.ai"
    data-show-powered-by="true"
  ></script>
</head>
<body>

<div class="demo-container">
  <div class="demo-header">
    <h1>Enhanced Chat Widget with Voice Recording</h1>
    <p>Experience the new chat widget with circular voice recording indicator and real-time voice detection</p>
  </div>

  <div class="feature-showcase">
    <h3>🎙️ Voice Recording Features</h3>
    <ul class="feature-list">
      <li>Circular voice recording indicator with smooth animations</li>
      <li>Real-time voice activity detection</li>
      <li>Color transitions during voice detection (blue gradient)</li>
      <li>Pulsing and breathing animation effects</li>
      <li>Microphone button with recording state feedback</li>
      <li>Responsive design for all screen sizes</li>
      <li>Accessibility features with ARIA labels</li>
    </ul>
  </div>

  <div class="demo-controls">
    <button onclick="setDemoUser()" class="demo-button primary">
      Set Demo User
    </button>
    <button onclick="clearDemoUser()" class="demo-button secondary">
      Clear User Info
    </button>
    <button onclick="refreshWidget()" class="demo-button secondary">
      Refresh Widget
    </button>
    <button onclick="testVoiceRecording()" class="demo-button secondary">
      Test Voice Recording
    </button>
  </div>
</div>

<!-- Zoomed Image Container -->
<div id="zoomedImageContainer" onclick="closeZoom()">
  <div class="imageWrapper">
    <img id="zoomedImage" src="" alt="" />
    <button class="closeButton" onclick="closeZoom()">
      <svg width="16px" height="16px" viewBox="0 0 24 24" fill="white" xmlns="http://www.w3.org/2000/svg">
        <path d="M20.7457 3.32851C20.3552 2.93798 19.722 2.93798 19.3315 3.32851L12.0371 10.6229L4.74275 3.32851C4.35223 2.93798 3.71906 2.93798 3.32854 3.32851C2.93801 3.71903 2.93801 4.3522 3.32854 4.74272L10.6229 12.0371L3.32856 19.3314C2.93803 19.722 2.93803 20.3551 3.32856 20.7457C3.71908 21.1362 4.35225 21.1362 4.74277 20.7457L12.0371 13.4513L19.3315 20.7457C19.722 21.1362 20.3552 21.1362 20.7457 20.7457C21.1362 20.3551 21.1362 19.722 20.7457 19.3315L13.4513 12.0371L20.7457 4.74272C21.1362 4.3522 21.1362 3.71903 20.7457 3.32851Z" fill="#FFFFFF"/>
      </svg>
    </button>
  </div>
</div>

<script>
  // Enhanced message listener with better error handling
  document.addEventListener('DOMContentLoaded', function () {
    window.addEventListener('message', (event) => {
      console.log('Received message:', event.data);

      try {
        if (event.data.action === 'zoomImage') {
          const zoomedImageContainer = document.getElementById('zoomedImageContainer');
          const zoomedImage = document.getElementById('zoomedImage');

          if (!zoomedImageContainer || !zoomedImage) {
            console.error('Zoom elements not found');
            return;
          }

          zoomedImage.src = event.data.src;

          // Remove existing alt text
          const existingAltText = document.getElementById('altText');
          if (existingAltText) {
            existingAltText.remove();
          }

          // Add alt text if provided
          if (event.data.alt) {
            const altTextDiv = document.createElement('div');
            altTextDiv.id = 'altText';
            altTextDiv.textContent = event.data.alt;
            document.querySelector('.imageWrapper').appendChild(altTextDiv);
          }

          zoomedImageContainer.style.display = 'flex';
        }
      } catch (error) {
        console.error('Error handling message:', error);
      }
    });
  });

  // Function to close zoomed image on click
  function closeZoom() {
    document.getElementById('zoomedImageContainer').style.display = 'none';
  }

  // Enhanced demo functions
  function setDemoUser() {
    if (typeof window.setUser === 'function') {
      const demoUser = {
        id: 'demo-user-123',
        name: 'John Doe',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
        preferences: {
          theme: 'light',
          language: 'en'
        },
        metadata: {
          source: 'demo',
          timestamp: new Date().toISOString()
        }
      };

      window.setUser(demoUser);
      console.log('Demo user set:', demoUser);
      showNotification('Demo user information has been set successfully!', 'success');
    } else {
      showNotification('Widget not ready. Please try again.', 'error');
    }
  }

  function clearDemoUser() {
    if (typeof window.clearUser === 'function') {
      window.clearUser();
      showNotification('User information cleared successfully!', 'success');
    } else {
      showNotification('Widget not ready. Please try again.', 'error');
    }
  }

  function refreshWidget() {
    // Send refresh message to widget
    const iframe = document.querySelector('#dx-shadow-widget-root iframe');
    if (iframe) {
      iframe.contentWindow.postMessage({ action: "refresh" }, '*');
      showNotification('Widget refreshed successfully!', 'success');
    } else {
      showNotification('Widget not found. Please reload the page.', 'error');
    }
  }

  function testVoiceRecording() {
    // Test voice recording functionality
    const micButton = document.querySelector('#dx-shadow-widget-root #mic-button');
    if (micButton) {
      micButton.click();
      showNotification('Voice recording test initiated! Check the chat bubble for the voice indicator.', 'info');
    } else {
      showNotification('Voice recording not available. Please ensure the widget is loaded.', 'warning');
    }
  }

  // Notification system
  function showNotification(message, type = 'info') {
    const colors = {
      success: '#10b981',
      error: '#ef4444',
      warning: '#f59e0b',
      info: '#3b82f6'
    };

    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${colors[type] || colors.info};
      color: white;
      padding: 16px 24px;
      border-radius: 12px;
      font-weight: 500;
      font-size: 14px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      z-index: 10000;
      transform: translateX(100%);
      transition: transform 0.3s ease;
      max-width: 300px;
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  // Load user info on page load
  window.onload = function () {
    const savedUserInfo = localStorage.getItem('userInfo');
    if (savedUserInfo && typeof window.setUser === 'function') {
      try {
        const userData = JSON.parse(savedUserInfo);
        window.setUser(userData);
        console.log('Loaded user from localStorage:', userData);
      } catch (error) {
        console.warn('Failed to parse userInfo from localStorage:', error);
      }
    }
  };
</script>

</body>
</html>
